package Tree;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class Main {
    public static void main(String[] args) {
        // 1. 准备完整数据集（14个样本）
        List<Sample> dataset = new ArrayList<>();

        // 晴天样本
        dataset.add(new Sample(Map.of("天气", "晴天", "温度", "高", "湿度", "高", "有风", "无"), "不打球"));
        dataset.add(new Sample(Map.of("天气", "晴天", "温度", "高", "湿度", "高", "有风", "有"), "不打球"));
        dataset.add(new Sample(Map.of("天气", "晴天", "温度", "中", "湿度", "高", "有风", "无"), "不打球"));
        dataset.add(new Sample(Map.of("天气", "晴天", "温度", "低", "湿度", "正常", "有风", "无"), "打球"));
        dataset.add(new Sample(Map.of("天气", "晴天", "温度", "中", "湿度", "正常", "有风", "有"), "打球"));

        // 阴天样本
        dataset.add(new Sample(Map.of("天气", "阴天", "温度", "高", "湿度", "高", "有风", "无"), "打球"));
        dataset.add(new Sample(Map.of("天气", "阴天", "温度", "中", "湿度", "高", "有风", "有"), "打球"));
        dataset.add(new Sample(Map.of("天气", "阴天", "温度", "低", "湿度", "正常", "有风", "无"), "打球"));
        dataset.add(new Sample(Map.of("天气", "阴天", "温度", "中", "湿度", "正常", "有风", "有"), "打球"));

        // 雨天样本
        dataset.add(new Sample(Map.of("天气", "雨天", "温度", "高", "湿度", "高", "有风", "有"), "不打球"));
        dataset.add(new Sample(Map.of("天气", "雨天", "温度", "高", "湿度", "高", "有风", "无"), "打球"));
        dataset.add(new Sample(Map.of("天气", "雨天", "温度", "中", "湿度", "正常", "有风", "有"), "不打球"));
        dataset.add(new Sample(Map.of("天气", "雨天", "温度", "低", "湿度", "正常", "有风", "无"), "打球"));
        dataset.add(new Sample(Map.of("天气", "雨天", "温度", "中", "湿度", "正常", "有风", "无"), "打球"));

        // 可用特征列表
        List<String> features = Arrays.asList("天气", "温度", "湿度", "有风");

        // 2. 训练模型
        DecisionTree tree = new DecisionTree();
        tree.train(dataset, features);

        // 3. 打印决策树结构
        System.out.println("决策树结构：");
        tree.printTree(tree.getRoot(), "");

        // 4. 预测新样本
        Sample newSample = new Sample(Map.of(
            "天气", "晴天",
            "温度", "中",  // 添加缺失特征
            "湿度", "正常",
            "有风", "无"
        ), null);

        String decision = tree.predict(newSample);
        System.out.println("\n预测结果: " + decision);
    }
} 