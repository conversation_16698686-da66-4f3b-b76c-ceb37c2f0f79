package Tree;

import java.util.HashMap;
import java.util.Map;

public class node {
    private String feature;             // 当前判断特征（非叶节点）
    private String label;               // 决策结果（叶节点）
    private Map<String, node> children; // 子节点映射

    public node() {
        this.children = new HashMap<>();
    }

    // 判断是否是叶节点
    public boolean isLeaf() {
        return label != null;
    }

    // toString方法
    @Override
    public String toString() {
        if (isLeaf()) {
            return "决策: " + label;
        } else {
            return "判断: " + feature;
        }
    }

    // Getters and Setters
    public String getFeature() {
        return feature;
    }

    public void setFeature(String feature) {
        this.feature = feature;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Map<String, node> getChildren() {
        return children;
    }

    public void setChildren(Map<String, node> children) {
        this.children = children;
    }
}
