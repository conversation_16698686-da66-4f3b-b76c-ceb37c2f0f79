## 设计过程

### 1. 节点类（TreeNode）

* **属性** ：

  * **id** ：节点的唯一标识符，用于追踪和调试。
  * **featureIndex** ：当前节点用于划分数据集的特征索引。
  * **children** ：一个字典，键是特征值，值是对应的子节点。
  * **label** ：如果当前节点是叶节点，则存储该节点的分类标签。
  * **depth** ：节点在决策树中的深度，根节点深度为 0。
* **方法** ：

  * **初始化方法** ：用于初始化节点的属性。
  * **设置和获取方法** ：用于设置和获取节点的属性，如设置特征索引、添加子节点、获取标签等。

### 2. 决策树类（DecisionTree）

* **属性** ：

  * **root** ：决策树的根节点。
  * **featureNames** ：特征的名称列表，用于调试和解释。
  * **nextNodeId** ：用于生成节点 ID 的计数器。
* **方法** ：

  * **初始化方法** ：初始化根节点和特征名称。
  * **fit 方法** ：用于训练决策树，接收数据集和特征名称作为输入，启动决策树的构建过程。
  * **predict 方法** ：用于对新的样本进行分类预测，返回预测的标签。
  * **_buildTree 方法** ：递归构建决策树的核心方法，接收当前数据集、当前节点和当前特征索引作为输入。
  * **_isPure 方法** ：判断当前数据集是否纯（所有样本都属于同一类别）。
  * **_getMajorityLabel 方法** ：获取数据集中出现次数最多的标签，用于叶节点的标签确定。

### 3. 数据处理器类（DataProcessor）

* **属性** ：

  * **data** ：存储读取的数据集。
  * **featureNames** ：存储特征的名称。
  * **targetName** ：存储目标标签的名称。
* **方法** ：

  * **读取数据方法** ：从文件（如 Excel 表）中读取数据，并将其转换为适合处理的格式（如二维数组）。
  * **预处理方法** ：处理缺失值和噪声数据，确保数据的完整性和准确性。
  * **划分数据方法** ：根据特征值将数据集划分为多个子集。

### 4. 特征选择器类（FeatureSelector）

* **属性** ：

  * **featureOrder** ：特征的顺序列表，用于按照特征索引顺序进行划分。
* **方法** ：

  * **获取特征顺序方法** ：返回特征的顺序列表。
  * **选择特征方法** ：根据当前特征索引选择当前划分特征。

### 设计思路解释

* **节点类** 是决策树的基本构建块，包含构建树所需的所有信息。
* **决策树类** 是核心类，负责训练和预测。它使用节点类来构建树的结构，并实现递归构建逻辑。
* **数据处理器类** 负责处理数据的输入和预处理，确保数据的质量和格式正确。
* **特征选择器类** 负责管理特征的选择顺序，确保按照预定的顺序进行特征划分。

现在我来详细解释一下决策树的构建过程。

## 决策树构建过程

决策树的构建是一个递归的过程，具体步骤如下：

### 1. 初始化

* 创建决策树的根节点，此时根节点没有任何特征信息和子节点。

### 2. 划分数据集

* 在当前节点，根据选定的特征将数据集划分为多个子集。每个子集对应一个特征值分支。

### 3. 递归构建子树

* 对于每个子集，创建一个子节点，并递归地调用构建决策树的函数来构建子树。

### 4. 特征选择

* 按照特征的索引顺序依次选择特征进行划分。在每次划分时，使用当前特征索引对应的特征。

### 5. 判断停止条件

* 如果当前数据集中的所有样本都属于同一类别，则将当前节点标记为叶节点，并设置其标签为该类别。
* 如果所有特征都已使用完，则将当前节点标记为叶节点，并设置其标签为数据集中出现次数最多的类别。

### 6. 更新特征索引

* 在每次划分后，更新特征索引，以便在下一次递归调用中选择下一个特征。

### 示例解释

假设我们有以下水果数据集：


| 颜色 | 形状   | 大小 | 水果种类 |
| ---- | ------ | ---- | -------- |
| 红色 | 圆形   | 大   | 苹果     |
| 黄色 | 长条形 | 中   | 香蕉     |
| 红色 | 心形   | 小   | 草莓     |
| 黄色 | 圆形   | 中   | 柠檬     |

### 构建过程

1. **初始化** ：创建根节点。
2. **选择特征** ：按照特征索引顺序，首先选择颜色特征。
3. **划分数据集** ：根据颜色特征的值（红色、黄色）将数据集划分为两个子集。
4. **递归构建子树** ：对于每个颜色子集，创建子节点，并递归地构建子树。

   * 对于红色子集，选择形状特征进行划分。
   * 对于黄色子集，选择形状特征进行划分。
5. **判断停止条件** ：如果某个子集中的所有样本都属于同一类别，则标记为叶节点。
6. **更新特征索引** ：在每次划分后，递增特征索引，以便选择下一个特征。

通过这个递归过程，最终构建出完整的决策树。每个节点代表一个特征判断，每个叶节点代表一个分类结果。

## 参考资料

https://www.cnblogs.com/zhangchaoyang/articles/2196631.html

!  [资料] (./资料/deepseek_mermaid_20250616_b988de.png)
