package Tree;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class test {
    public static void main(String[] args) {
        // 1. 准备西瓜数据集（15个样本）
        List<Sample> watermelonDataset = new ArrayList<>();

        // 正例（好瓜）
        watermelonDataset.add(new Sample(Map.of(
                "色泽", "青绿", "根蒂", "蜷缩", "敲声", "浊响",
                "纹理", "清晰", "脐部", "凹陷", "触感", "硬滑"), "好瓜"));

        watermelonDataset.add(new Sample(Map.of(
                "色泽", "乌黑", "根蒂", "蜷缩", "敲声", "沉闷",
                "纹理", "清晰", "脐部", "凹陷", "触感", "硬滑"), "好瓜"));

        watermelonDataset.add(new Sample(Map.of(
                "色泽", "乌黑", "根蒂", "蜷缩", "敲声", "浊响",
                "纹理", "清晰", "脐部", "凹陷", "触感", "硬滑"), "好瓜"));

        watermelonDataset.add(new Sample(Map.of(
                "色泽", "青绿", "根蒂", "稍蜷", "敲声", "浊响",
                "纹理", "清晰", "脐部", "稍凹", "触感", "软粘"), "好瓜"));

        watermelonDataset.add(new Sample(Map.of(
                "色泽", "浅白", "根蒂", "蜷缩", "敲声", "浊响",
                "纹理", "清晰", "脐部", "凹陷", "触感", "硬滑"), "好瓜"));

        watermelonDataset.add(new Sample(Map.of(
                "色泽", "青绿", "根蒂", "稍蜷", "敲声", "浊响",
                "纹理", "稍糊", "脐部", "稍凹", "触感", "软粘"), "好瓜"));

        // 反例（坏瓜）
        watermelonDataset.add(new Sample(Map.of(
                "色泽", "浅白", "根蒂", "硬挺", "敲声", "清脆",
                "纹理", "模糊", "脐部", "平坦", "触感", "硬滑"), "坏瓜"));

        watermelonDataset.add(new Sample(Map.of(
                "色泽", "浅白", "根蒂", "蜷缩", "敲声", "沉闷",
                "纹理", "稍糊", "脐部", "稍凹", "触感", "硬滑"), "坏瓜"));

        watermelonDataset.add(new Sample(Map.of(
                "色泽", "青绿", "根蒂", "硬挺", "敲声", "清脆",
                "纹理", "清晰", "脐部", "平坦", "触感", "软粘"), "坏瓜"));

        watermelonDataset.add(new Sample(Map.of(
                "色泽", "浅白", "根蒂", "稍蜷", "敲声", "浊响",
                "纹理", "模糊", "脐部", "平坦", "触感", "硬滑"), "坏瓜"));

        watermelonDataset.add(new Sample(Map.of(
                "色泽", "乌黑", "根蒂", "稍蜷", "敲声", "浊响",
                "纹理", "稍糊", "脐部", "稍凹", "触感", "软粘"), "坏瓜"));

        watermelonDataset.add(new Sample(Map.of(
                "色泽", "青绿", "根蒂", "蜷缩", "敲声", "沉闷",
                "纹理", "稍糊", "脐部", "凹陷", "触感", "硬滑"), "坏瓜"));

        watermelonDataset.add(new Sample(Map.of(
                "色泽", "浅白", "根蒂", "蜷缩", "敲声", "浊响",
                "纹理", "模糊", "脐部", "平坦", "触感", "软粘"), "坏瓜"));

        watermelonDataset.add(new Sample(Map.of(
                "色泽", "乌黑", "根蒂", "稍蜷", "敲声", "浊响",
                "纹理", "清晰", "脐部", "稍凹", "触感", "软粘"), "坏瓜"));

        watermelonDataset.add(new Sample(Map.of(
                "色泽", "青绿", "根蒂", "硬挺", "敲声", "清脆",
                "纹理", "稍糊", "脐部", "平坦", "触感", "硬滑"), "坏瓜"));

        // 使用西瓜特征列表
        List<String> watermelonFeatures = Arrays.asList(
                "色泽", "根蒂", "敲声", "纹理", "脐部", "触感");

        // 2. 训练西瓜决策树模型
        DecisionTree watermelonTree = new DecisionTree();
        watermelonTree.train(watermelonDataset, watermelonFeatures);

        // 3. 打印决策树结构
        System.out.println("西瓜决策树结构：");
        watermelonTree.printTree(watermelonTree.getRoot(), "");

        // 4. 预测新样本
        // 测试样本1：明显的好瓜特征
        Sample testWatermelon1 = new Sample(Map.of(
                "色泽", "青绿",
                "根蒂", "蜷缩",
                "敲声", "浊响",
                "纹理", "清晰",
                "脐部", "凹陷",
                "触感", "硬滑"), null);

        // 测试样本2：明显的坏瓜特征
        Sample testWatermelon2 = new Sample(Map.of(
                "色泽", "浅白",
                "根蒂", "硬挺",
                "敲声", "清脆",
                "纹理", "模糊",
                "脐部", "平坦",
                "触感", "硬滑"), null);

        // 测试样本3：边界情况
        Sample testWatermelon3 = new Sample(Map.of(
                "色泽", "乌黑",
                "根蒂", "稍蜷",
                "敲声", "浊响",
                "纹理", "稍糊",
                "脐部", "稍凹",
                "触感", "软粘"), null);

        // 进行预测
        System.out.println("\n测试样本1预测: " + watermelonTree.predict(testWatermelon1));
        System.out.println("测试样本2预测: " + watermelonTree.predict(testWatermelon2));
        System.out.println("测试样本3预测: " + watermelonTree.predict(testWatermelon3));
    }
}