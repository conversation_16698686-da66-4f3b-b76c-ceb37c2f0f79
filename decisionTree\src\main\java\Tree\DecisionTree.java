package Tree;

import java.util.*;

public class DecisionTree {
    private node root;

    public DecisionTree() {
        this.root = null;
    }

    // 获取根节点
    public node getRoot() {
        return root;
    }

    // 计算熵
    private double calculateEntropy(List<Sample> samples) {
        Map<String, Integer> counts = new HashMap<>();
        for (Sample s : samples) {
            counts.put(s.getLabel(), counts.getOrDefault(s.getLabel(), 0) + 1);
        }

        double entropy = 0.0;
        int total = samples.size();
        for (int count : counts.values()) {
            double p = (double) count / total;
            entropy -= p * (Math.log(p) / Math.log(2));
        }
        return entropy;
    }

    // 分割数据集
    private Map<String, List<Sample>> splitByFeature(List<Sample> samples, String feature) {
        Map<String, List<Sample>> subsets = new HashMap<>();

        for (Sample s : samples) {
            String value = s.getFeatures().get(feature);
            subsets.computeIfAbsent(value, k -> new ArrayList<>()).add(s);
        }

        return subsets;
    }

    // 选择最佳特征
    private String selectBestFeature(List<Sample> samples, List<String> features) {
        //计算熵值
        double baseEntropy = calculateEntropy(samples);
        double maxGain = -1;
        String bestFeature = null;

        for (String feature : features) {
            Map<String, List<Sample> > subsets = splitByFeature(samples, feature);
            double subsetEntropy = 0.0;
            //计算子集的熵值
            for (List<Sample> subset : subsets.values()) {
                double weight = (double) subset.size() / samples.size();
                subsetEntropy += weight * calculateEntropy(subset);
            }

            //计算信息增益
            double gain = baseEntropy - subsetEntropy;
            if (gain > maxGain) {
                maxGain = gain;
                bestFeature = feature;
            }
        }

        return bestFeature;
    }

    // 构建决策树
    public node buildTree(List<Sample> samples, List<String> features) {
        // 情况1：所有样本同类别
        boolean allSame = true;
        String firstLabel = samples.get(0).getLabel();
        for (Sample s : samples) {
            //s.getLabel().equals(firstLabel) ==>ture
            //!s.getLabel().equals(firstLabel) ==>false
            //发现不一样就会跳出
            if (!s.getLabel().equals(firstLabel)) {
                allSame = false;
                break;
            }
        }
        if (allSame) {
            //一样的label就会新建一个node，设置label=第一个标签
            node leaf = new node();
            leaf.setLabel(firstLabel);
            return leaf;
        }

        // 情况2：没有可用特征
        //是否为空，空为ture
        //"创建了一个保存了最大次数标签的名字"
        if (features.isEmpty()) {
            node leaf = new node();
            Map<String, Integer> counts = new HashMap<>();
            for (Sample s : samples) {
                counts.put(s.getLabel(), counts.getOrDefault(s.getLabel(), 0) + 1);
            }
            leaf.setLabel(Collections.max(counts.entrySet(), Map.Entry.comparingByValue()).getKey());
            return leaf;
        }

        // 选择最佳特征
        String bestFeature = selectBestFeature(samples, features);

        node node = new node();
        node.setFeature(bestFeature);

        // 创建新特征列表
        List<String> newFeatures = new ArrayList<>(features);
        newFeatures.remove(bestFeature);

        // 按特征值分割数据集
        Map<String, List<Sample>> subsets = splitByFeature(samples, bestFeature);

        // 为每个特征值创建子树
        for (Map.Entry<String, List<Sample>> entry : subsets.entrySet()) {
            String value = entry.getKey();
            List<Sample> subset = entry.getValue();

            if (subset.isEmpty()) {
                node leaf = new node();
                Map<String, Integer> counts = new HashMap<>();
                for (Sample s : samples) {
                    counts.put(s.getLabel(), counts.getOrDefault(s.getLabel(), 0) + 1);
                }
                leaf.setLabel(Collections.max(counts.entrySet(), Map.Entry.comparingByValue()).getKey());
                node.getChildren().put(value, leaf);
            } else {
                node.getChildren().put(value, buildTree(subset, newFeatures));
            }
        }

        return node;
    }

    // 预测方法
    public String predict(Sample sample, node node) {
        if (node.isLeaf()) {
            return node.getLabel();
        }

        String featureValue = sample.getFeatures().get(node.getFeature());
        if (!node.getChildren().containsKey(featureValue)) {
            return "未知";
        }

        return predict(sample, node.getChildren().get(featureValue));
    }

    // 打印决策树
    public void printTree(node node, String indent) {
        if (node.isLeaf()) {
            System.out.println(indent + node);  // 使用toString
            return;
        }
      
        // 打印当前节点信息（包含样本分布）
        System.out.printf("%s%s (样本数: %d)%n", 
            indent, node, countSamples(node));
      
        for (Map.Entry<String, node> entry : node.getChildren().entrySet()) {
            // 打印分支详情
            System.out.printf("%s└─ 分支 [%s] → ", indent, entry.getKey());
            printTree(entry.getValue(), indent + "    ");
        }
    }

    // 新增方法：计算节点下样本数（递归）
    private int countSamples(node node) {
        if (node.isLeaf()) return 1;
      
        int count = 0;
        for (node child : node.getChildren().values()) {
            count += countSamples(child);
        }
        return count;
    }

    // 训练模型
    public void train(List<Sample> samples, List<String> features) {
        this.root = buildTree(samples, features);
    }

    // 预测新样本
    public String predict(Sample sample) {
        return predict(sample, root);
    }
} 