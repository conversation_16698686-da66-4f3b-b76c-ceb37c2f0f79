package Tree;

import java.util.Map;

public class Sample {
    private Map<String, String> features; // 特征字典 {特征名:特征值}
    private String label;                // 类别标签

    public Sample(Map<String, String> features, String label) {
        this.features = features;
        this.label = label;
    }

    public Map<String, String> getFeatures() {
        return features;
    }

    public void setFeatures(Map<String, String> features) {
        this.features = features;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
} 